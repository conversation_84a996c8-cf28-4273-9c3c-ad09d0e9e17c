apiVersion: apps/v1
kind: Deployment
metadata:
  name: interviewbot-frontend
  namespace: interviewbot
spec:
  replicas: 2
  selector:
    matchLabels:
      app: interviewbot-frontend
  template:
    metadata:
      labels:
        app: interviewbot-frontend
    spec:
      containers:
      - name: frontend
        image: gcr.io/PROJECT_ID/interviewbot-frontend:latest
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: interviewbot-frontend-service
  namespace: interviewbot
spec:
  selector:
    app: interviewbot-frontend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer

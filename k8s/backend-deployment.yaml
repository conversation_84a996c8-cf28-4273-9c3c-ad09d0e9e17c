apiVersion: apps/v1
kind: Deployment
metadata:
  name: interviewbot-backend
  namespace: interviewbot
spec:
  replicas: 2
  selector:
    matchLabels:
      app: interviewbot-backend
  template:
    metadata:
      labels:
        app: interviewbot-backend
    spec:
      containers:
      - name: backend
        image: gcr.io/PROJECT_ID/interviewbot-backend:latest
        ports:
        - containerPort: 8002
        env:
        - name: DATABASE_URL
          value: "http://interviewbot-database-service:8003"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: interviewbot-backend-service
  namespace: interviewbot
spec:
  selector:
    app: interviewbot-backend
  ports:
  - protocol: TCP
    port: 8002
    targetPort: 8002
  type: LoadBalancer

apiVersion: apps/v1
kind: Deployment
metadata:
  name: interviewbot-database
  namespace: interviewbot
spec:
  replicas: 1
  selector:
    matchLabels:
      app: interviewbot-database
  template:
    metadata:
      labels:
        app: interviewbot-database
    spec:
      containers:
      - name: database
        image: gcr.io/PROJECT_ID/interviewbot-database:latest
        ports:
        - containerPort: 8003
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: database-storage
          mountPath: /app/data
      volumes:
      - name: database-storage
        persistentVolumeClaim:
          claimName: database-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: interviewbot-database-service
  namespace: interviewbot
spec:
  selector:
    app: interviewbot-database
  ports:
  - protocol: TCP
    port: 8003
    targetPort: 8003
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: database-pvc
  namespace: interviewbot
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard-rwo

# Simplified nginx configuration for Cloud Run deployment
# This serves the React static files only
# API calls are handled directly by the React app using REACT_APP_API_BASE_URL

server {
  listen 3000;
  listen [::]:3000;
  server_name _;
  client_max_body_size 5000M;

  # Health check endpoint for Cloud Run
  location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
  }

  # Serve React static files
  location / {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /index.html;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
  }

  # Handle static assets with caching
  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    root /usr/share/nginx/html;
    expires 1y;
    add_header Cache-Control "public, immutable";
  }
}

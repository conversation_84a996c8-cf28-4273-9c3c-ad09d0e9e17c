"""
InterviewBot Backend Application

A Flask-based backend service for generating AI-powered interview questions
based on candidate resumes and job descriptions.
"""
import base64
import io
from flask import Flask, request, jsonify, abort, send_file
from flask_cors import CORS

# Import configuration and services
from config.settings import get_config
from api.candidate_routes import candidate_bp
from api.interview_routes import interview_bp

# Import legacy modules (to be refactored)
from llm_inference import *
from utils import *
import json
import requests

# Initialize Flask app
app = Flask(__name__)
config = get_config()
app.config.from_object(config)

# Enable CORS for frontend communication
# Allow both development and production origins
cors_origins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app',
    'https://interviewbot-frontend-968473796231.us-central1.run.app',
    'https://interviews.creospan.com'  # Custom domain if used
]
CORS(app, supports_credentials=True, origins=cors_origins)

# Register blueprints
app.register_blueprint(candidate_bp, url_prefix='/api/candidates')
app.register_blueprint(interview_bp, url_prefix='/api/interview')

# Legacy configuration (to be removed after full migration)
DATABASE_API_URL = config.DATABASE_API_URL
JD_DATABSE_API = config.JD_DATABASE_API
AR_DATABASE_API = config.AR_DATABASE_API
# simple db crud operations are just forwarded to the db server
def forward_request(DATABASE_API_URL, route, method, json_data=None):
    try:
        if method == 'GET':
            response = requests.get(f"{DATABASE_API_URL}/{route}")
        elif method == 'POST':
            response = requests.post(f"{DATABASE_API_URL}/{route}", json=json_data)
        else:
            return abort(400, "Invalid HTTP method")

        response.raise_for_status()  # Raise an HTTPError for bad responses (4xx and 5xx)

        return response.json(), response.status_code

    except requests.RequestException as e:
        return abort(500, f"Error forwarding request: {e}")

def is_valid_json(s):
    try:
        json.loads(s)
        return True
    except json.JSONDecodeError:
        return False
    
@app.route('/getAll', methods=['GET'])
def getAllCandidates():
    data, status_code = forward_request(DATABASE_API_URL,"getAll", "GET")
    return jsonify(data), status_code

@app.route('/getCandidate', methods=['POST'])
def get_data():
    json_data = request.get_json()
    data, status_code = forward_request(DATABASE_API_URL, "getCandidate", "POST", json_data)
    return jsonify(data), status_code

@app.route('/saveCandidate', methods=['POST'])
def saveCandidateData():
    json_data = request.get_json()
    data, status_code = forward_request(DATABASE_API_URL, "saveCandidate", "POST", json_data)
    return jsonify(data), status_code

@app.route('/deleteInterview', methods=['POST'])
def delete_interview():
    json_data = request.get_json()
    data, status_code = forward_request(DATABASE_API_URL, "deleteInterview", "POST", json_data)
    return jsonify(data), status_code

# This section covers Presets 
@app.route('/getAllPresets', methods=['GET'])
def getAllPresets():
    data, status_code = forward_request(JD_DATABSE_API, "getAllPresets", "GET")
    return jsonify(data), status_code

@app.route('/getPreset', methods=['POST'])
def get_Presetdata():
    json_data = request.get_json()
    data, status_code = forward_request(JD_DATABSE_API, "getPreset", "POST", json_data)
    return jsonify(data), status_code

@app.route('/saveJD', methods=['POST'])
def savePresetData():
    json_data = request.get_json()
    data, status_code = forward_request(JD_DATABSE_API, "saveJD", "POST", json_data)
    return jsonify(data), status_code

@app.route('/deleteJD', methods=['POST'])
def delete_JD():
    json_data = request.get_json()
    data, status_code = forward_request(JD_DATABSE_API, "deleteJD", "POST", json_data)
    return jsonify(data), status_code

# This section deals with the Preset Approval Process for Power Users

@app.route('/getAllARPresets', methods=['GET'])
def getAllARPresets():
    
    data, status_code = forward_request(AR_DATABASE_API, "getAllARPresets", "GET")
    return jsonify(data), status_code



@app.route('/getARPreset', methods=['POST'])
def get_ARPresetdata():
    json_data = request.get_json()
    data, status_code = forward_request(AR_DATABASE_API, "getARPreset", "POST", json_data)
    return jsonify(data), status_code

@app.route('/saveAR', methods=['POST'])
def saveARPresetData():
    json_data = request.get_json()
    data, status_code = forward_request(AR_DATABASE_API, "saveAR", "POST", json_data)
    return jsonify(data), status_code

@app.route('/deleteAR', methods=['POST'])
def delete_AR():
    json_data = request.get_json()
    data, status_code = forward_request(AR_DATABASE_API, "deleteAR", "POST", json_data)
    return jsonify(data), status_code


# This route handles uploading JD and resume to generate base questions and data
# This is the heart of all the apps functionality. First it parses documents then it generates the prompts with the files contents.
# Next it loops through all the prompts and adds the responses to an array that is then returned
@app.route('/uploadQA', methods=['POST'])
def uploadRepo():
    if 'resumeUpload' in request.files:
        resume = request.files.get('resumeUpload')
    else:
        resume = request.form.get('resumeUpload')

    if 'jobDescription' in request.files:
        jobDescription = request.files.get('jobDescription')
    else:
        jobDescription = request.form.get('jobDescription')

    if 'linkedinProfile' in request.files:
        linkedInID = request.files.get('linkedinProfile')
    else:
        linkedInID = request.form.get('linkedinProfile')
       
    final = []

    if(jobDescription and resume):
        try:
            if (resume is not None):
                resume_text = check_file_extension(resume)
                if not resume_text or len(resume_text.strip()) == 0:
                    return jsonify({"error": "Resume processing failed", "message": "Resume file is empty or could not be processed"}), 400
            if (jobDescription is not None):
                job_text = check_file_extension(jobDescription)
                if not job_text or len(job_text.strip()) == 0:
                    return jsonify({"error": "Job description processing failed", "message": "Job description file is empty or could not be processed"}), 400
        except Exception as e:
            print(f"Error processing files: {str(e)}")
            return jsonify({"error": "File processing failed", "message": f"Failed to process uploaded files: {str(e)}"}), 500

        try:
            prompts = get_main_prompts(resume_text, job_text, linkedInID)
        except Exception as e:
            print(f"Error generating prompts: {str(e)}")
            return jsonify({"error": "Prompt generation failed", "message": f"Failed to generate prompts: {str(e)}"}), 500

        for prompt in prompts:
            try:
                response = openai_api_GPT4_st(prompt)
               # response = google_gemeni_st(prompt)
            except Exception as e:
                print(f"Error calling LLM API: {str(e)}")
                return jsonify({"error": "LLM API call failed", "message": f"Failed to call LLM API: {str(e)}"}), 500

            # Handle OpenAI API response format
            if isinstance(response, dict) and 'choices' in response:
                response = response['choices'][0]['message']['content']

            try:
                 response = json.loads(response)
            except json.JSONDecodeError:
                 pass
            
            if isinstance(response, (list, dict)):
                 final.append(response)
                 continue
            if isinstance(response, str):
                 response = response.replace('```json', '').replace('```', '').strip()
                 for _ in range(3):
                     try:
                         response = json.loads(response)
                     except (json.JSONDecodeError, TypeError):
                         break
                     if isinstance(response, (list, dict)):
                         break
                 else:
                     if is_valid_json(response):
                         response = json.loads(response)
                     else:
                         response = response

            if isinstance(response, str) and ("error" in response.lower() and not response.strip().startswith('[')):
                 print('-------------------')
                 print('ERROR:', response)
                 print("TYPE OF RESPONSE:", type(response))
                 print('-------------------')
                 return jsonify({"error": response, "message": "LLM API error occurred"}), 500
            
            
            answer = json.dumps(response)
            # answer = json.loads(response)
           
        #     data = json.loads(response)
            # print("data: ", data)
           #  answer = json.loads(answer)
            
             #answer = data['choices'][0]['message']['content']
        #     answer = data.replace('```', '').replace('json','')
        #     print(answer)
            jsonAnswer = ''
             # Process of cleaning the llm responses into DTOs
             # If prompts are changed (specifically changed to return different response structure) then this will need to be altered
            if '[' not in answer:
                 if '{' in answer:
                     # This is the 5th question that extracts basic candidate info
                     cleaned_item = answer.replace('```', '').replace('json','').replace(r'\n', '\\n').replace('**','').replace(r'* ','-').replace('\t', '\\t').replace('\r', '').strip()
                   #  print(cleaned_item)
                     try:
                         jsonAnswer = json.loads(cleaned_item)
                     except json.JSONDecodeError as e:
                         print( '-------------------------', '\n' ,json.JSONDecodeError , '\n' , e)
                         print(cleaned_item, '\n', '-------------------------')
                         return jsonify({"error": "JSON parsing failed", "message": f"Failed to parse LLM response: {str(e)}", "raw_response": cleaned_item}), 500
                     final.append(jsonAnswer)
                 else:
                    jsonAnswer = answer.replace('```', '').replace('json','').replace(r'\n', '\n').replace('**','').replace(r'* ','-').replace('\t', '\\t').replace('\r', '').replace('\\','').strip()
                    print(jsonAnswer)
                    final.append(jsonAnswer)
            else:
                cleaned_item = answer.replace('```', '').replace('json','').replace(r'\n', '').replace('**','').replace(r'* ','-').replace('\t', '\\t').replace('\r', '').replace('\n','').strip()
                try:
                    jsonAnswer = json.loads(cleaned_item)
                    print('\n',jsonAnswer)
                except json.JSONDecodeError as e:
                    print( '-------------------------', '\n' ,json.JSONDecodeError , '\n' , e)
                    print(cleaned_item, '\n', '-------------------------')
                    return jsonify({"error": "JSON parsing failed", "message": f"Failed to parse LLM response: {str(e)}", "raw_response": cleaned_item}), 500
                final.append(jsonAnswer)
    else:
         return jsonify({"error": "Missing files", "message": "Both resume and job description files are required"}), 400

    # Validate final response structure before returning
    if not final or len(final) < 7:
        print(f"Warning: Incomplete response structure. Expected 7 elements, got {len(final) if final else 0}")
        return jsonify({"error": "Incomplete response", "message": "Failed to generate complete interview data"}), 500

    print(f"Successfully generated interview data with {len(final)} elements")
    return jsonify(final), 200

# This route is automatically called after /uploadQA **IF** a linkedin profile is found in the resume
# If one is not detected, users have the option of inputing the linkedin profile id manually, which will then call this route
# Users can also choose not to input the linkedin id in which case this functionality is skipped
# COMMENTED OUT: LinkedIn functionality disabled for new interviews
# @app.route('/assessLinkedin', methods=['POST'])
# def assessLinked():
#     linkedinID = request.form.get('linkedinProfile')
#     if 'resumeUpload' in request.files:
#         resume = request.files.get('resumeUpload')
#     else:
#         resume = request.form.get('resumeUpload')
#
#     if (resume is not None and linkedinID is not None):
#         resume_text = check_file_extension(resume)
#         linkedin_data = get_profile(linkedinID)
#
#         if('Person profile does not exist' in linkedin_data):
#             return {'invalid': 'Provided LinkedIn Profile is invalid.'}, 200
#         prompt = get_linkedin_prompt(resume_text, linkedin_data)
#
#         response = google_gemeni_api_linkedIn(prompt)
#         # This section is for gemini data cleaning (for GPT you dont need this)
#         response = json.dumps(response)
#         if response.startswith("```json"):
#             response = response[len("```json"):].strip()
#         elif response.startswith("```"):
#             response = response[len("```"):].strip()
#         if response.endswith("```"):
#             response = response[:-3].strip()
#         # END Section
#         if("error" in response):
#             return response
#
#         #response_string = json.dumps(response)
#         data = json.loads(response)
#         answer = data.replace('```', '').replace('json','').replace('\\','').replace(r'**','').replace(r'* ', '-')
#         # answer = data['choices'][0]['message']['content']  Also for GPT
#
#         return {'assessment': answer}, 200
#     else:
#         return jsonify({"Error": "Missing file(s)"}), 500

# LinkedIn assessment route is now disabled - returns empty assessment
@app.route('/assessLinkedin', methods=['POST'])
def assessLinked():
    return {'assessment': ''}, 200


@app.route('/regenerateTechnical', methods = ['POST'])
def getTechnical():
    if 'report' in request.files:
        report = request.files.get('report')
    else:
        report = request.form.get('report')

    if 'jobDescription' in request.files:
        jobDescription = request.files.get('jobDescription')
    else:
        jobDescription = request.form.get('jobDescription')

    if 'questionNumber' in request.files:
        number = request.files.get('questionNumber')
    else:
        number = request.form.get('questionNumber')

    if(jobDescription and report):
        if (report is not None):
            report = check_file_extension(report)
        if (jobDescription is not None):
            job_text = check_file_extension(jobDescription)
        
        prompt = regenerate_technical(job_text, report, number)
        response = openai_api_GPT4_st(prompt)
        res = clean_response(response)
        print(res)
        return res

@app.route('/regenerateBehavioral', methods = ['POST'])
def getBehavioral():
    if 'report' in request.files:
        report = request.files.get('report')
    else:
        report = request.form.get('report')

    if 'jobDescription' in request.files:
        jobDescription = request.files.get('jobDescription')
    else:
        jobDescription = request.form.get('jobDescription')

    if 'questionNumber' in request.files:
        number = request.files.get('questionNumber')
    else:
        number = request.form.get('questionNumber')
    if(jobDescription and report):
        if (report is not None):
            report = check_file_extension(report)
        if (jobDescription is not None):
            job_text = check_file_extension(jobDescription)
        
        prompt = regenerate_behavioral(job_text, report, number)
        response = openai_api_GPT4_st(prompt)
        res = clean_response(response)
        print(res)
        return res
    
@app.route('/getImage', methods=['POST'])
def getImage():
    if 'resumeUpload' in request.files:
        resume = request.files.get('resumeUpload')
        resume.save("/tmp"+resume.filename)
        
        
    if resume is not None:
        images = extract_PDFimages("/tmp"+ resume.filename)
        print(id(images))
        if images:
            image_data = "/tmp/Image-1.png"
            base64_image = convertToBytes(image_data)
            print(base64_image)
            return {"image": base64_image}, 200

        else:
            return jsonify({'Error': 'No images found'}), 500


def convertToBytes(image_data):
   
        image_file_path = image_data

        # Read the image file and encode it to base64
        with open(image_file_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_image = base64.b64encode(image_data).decode('utf-8')
        
        return base64_image
        
        
    
def clean_response(response):

    final = []

    # Handle OpenAI API response format
    if isinstance(response, dict) and 'choices' in response:
        response = response['choices'][0]['message']['content']

    try:
        response = json.loads(response)
    except json.JSONDecodeError:
        pass
    
    if isinstance(response, (list, dict)):
        return response
    if isinstance(response, str):
        response = response.replace('```json', '').replace('```', '').strip()
        for _ in range(3):
            try:
                response = json.loads(response)
            except (json.JSONDecodeError, TypeError):
                    break
            if isinstance(response, (list, dict)):
                break
            else:
                if is_valid_json(response):
                    response = json.loads(response)
                else:
                    response = response

        if isinstance(response, str) and ("error" in response.lower() and not response.strip().startswith('[')):
            print('-------------------')
            print('ERROR:', response)
            print("TYPE OF RESPONSE:", type(response))
            print('-------------------')
            return response
        
        
        answer = json.dumps(response)
      
        jsonAnswer = ''
        # Process of cleaning the llm responses into DTOs
        # If prompts are changed (specifically changed to return different response structure) then this will need to be altered
        if '[' not in answer:
            if '{' in answer:
                # This is the 5th question that extracts basic candidate info
                cleaned_item = answer.replace('```', '').replace('json','').replace(r'\n', '\\n').replace('**','').replace(r'* ','-').replace('\t', '\\t').replace('\r', '').strip()
                #  print(cleaned_item)
                try: 
                    jsonAnswer = json.loads(cleaned_item)
                except json.JSONDecodeError as e:
                    print( '-------------------------', '\n' ,json.JSONDecodeError , '\n' , e)
                    print(cleaned_item, '\n', '-------------------------')
                final.append(jsonAnswer)
            else:
                jsonAnswer = answer.replace('```', '').replace('json','').replace(r'\n', '\n').replace('**','').replace(r'* ','-').replace('\t', '\\t').replace('\r', '').replace('\\','').strip()
                #  print(jsonAnswer)
                final.append(jsonAnswer)
        else:
            cleaned_item = answer.replace('```', '').replace('json','').replace(r'\n', '').replace('**','').replace(r'* ','-').replace('\t', '\\t').replace('\r', '').replace('\n','').strip()
            try: 
                #  print(cleaned_item)
                jsonAnswer = json.loads(cleaned_item)
                print('skills answer: \n',jsonAnswer)
            except json.JSONDecodeError as e:
                print( '-------------------------', '\n' ,json.JSONDecodeError , '\n' , e)
                print(cleaned_item, '\n', '-------------------------')
            final.append(jsonAnswer)
    else:
        return jsonify({"Error": "Missing file(s)"}), 500

    return jsonify(final), 200

# Hold off on these routes until RAG architecture is implemented

# TO DO: Route to generate additional general question

# TO DO: Route to generate additional technical question

# Flask is run on 8002 intentally but gunicorn runs the app externally on 8000
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8002, debug=False)
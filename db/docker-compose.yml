version: '1'

# YOU MUST define the external network "interviewGPT_network" manually on the command line so the 2 docker-compose files can communicate
# Reason being that we can update the frontend and backend without worrying about changing the db container
# If the db container is re-built, the data in the database will be lost
# We will need to implement a db backup at some point (cloud storage or another methodology)
networks:
  interviewGPT_network:
    external: true

services:
  # In the backend code "main.py" the database uri is "database:8001" because gun<PERSON> binds the flask port 8003 on 8001
  # Since the two containers are on the same docker network, the service named below "database" can be inferenced directly
  database:
    image: database
    ports:
      - 8003:8003
    volumes:
      - database_data:/app/data
    networks:
      - interviewGPT_network

  jddatabase:
    image: jddatabase
    ports:
      - 8004:8004
    volumes:
      - jddatabase_data:/app/data
    networks:
      - interviewGPT_network

  ardatabase:
    image: ardatabase
    ports:
      - 8005:8005
    volumes:
      - ardatabase_data:/app/data
    networks:
      - interviewGPT_network

volumes:
  database_data:
  jddatabase_data:
  ardatabase_data:
from flask import Flask, request, jsonify
import json
import sqlite3

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 5 * 1024 * 1024 * 1024  # 5 GB

def connect_to_db():
    """
    Connects to the candidate database.

    Returns:
        conn: Connection object to the candidate database.
    """
    conn = sqlite3.connect('./JDPresets')
    return conn


def create_db_table():
    """
    Creates a database table called 'JDPresets' if it doesn't already exist.
    The table has the following columns:
    id INTEGER PRIMARY KEY,
    uuid TEXT,
    date TEXT,
    creator TEXT,
    job TEXT,
    desc TEXT,
    clientName TEXT,
    clientManager TEXT,
    status TEXT,
    mandQS TEXT,
    data TEXT
    """
    try:
        conn = connect_to_db()
        conn.execute('''CREATE TABLE IF NOT EXISTS JDPresets (
                    id INTEGER PRIMARY KEY,
                    uuid TEXT,
                    date TEXT,
                    creator TEXT,
                    job TEXT,
                    desc TEXT,
                    clientName TEXT,
                    clientManager TEXT,
                    status TEXT,
                    mandQS TEXT,
                    data TEXT
                )''')

        conn.commit()
    except sqlite3.Error as e:
        print(f"Error creating the 'JDPresets' table: {e}")
    finally:
        conn.close()
        

# This route gets all interviews general data
@app.route('/getAllPresets', methods=['GET'])
def getAllJobPresets():
    """
    Retrieves all candidates from the database and returns them as a JSON response.

    Returns:
        A tuple containing the JSON response with all candidates and the HTTP status code 200.
    """
    create_db_table()
    jobs = []
    try:
        conn = connect_to_db()
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        cur.execute("SELECT * FROM JDPresets")
        rows = cur.fetchall()

        # convert row objects to dictionary
        for i in rows:
            preset = {}
            preset["Created_By"] = i["creator"]
            preset["date"] = i["date"]
            preset["Job_Title"] = i["job"]
            preset["Job_Description"] = i["desc"]
            preset["Client_Name"] = i["clientName"]
            preset["Client_Manager"] = i["clientManager"]
            preset["Mandatory_QS"] = i["mandQS"]
            preset["uuid"] = i["uuid"]
            preset["Status"] = i["status"]
           
            jobs.append(preset)

    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        jobs = []

    return jsonify(jobs), 200

# This route gets a single interview
@app.route('/getPreset', methods=['POST'])
def get_data():
    """
    Retrieves JD data from the database based on the provided UUID.

    Returns:
        A JSON response containing the JD data.
    """
    try:
        json_data = request.get_json()
        uuid = json_data.get('uuid')

        conn = connect_to_db()
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        cur.execute("SELECT data FROM JDPresets WHERE uuid = ?", (uuid,))
        row = cur.fetchone()

        JDData = row["data"]
        json_data = json.loads(JDData)
    except:
        json_data = []

    return jsonify({"JDData": json_data}), 200

# This route handles adding and editing data in candidate db
@app.route('/saveJD', methods=['POST'])
def saveCandidateData():
    print("SUCCESS")
    create_db_table()
    try:
        json_data = request.get_json()
        JD_Uuid = json_data.get('JD_Uuid')
        JDName = json_data.get('Job_Title')
        JDDesc = json_data.get('Job_Description')
        Date = json_data.get('date')
        creator = json_data.get('creator')
        clientName = json_data.get('Client_Name')
        clientManager = json_data.get('Client_Manager')
        mandQS = json_data.get("Mandatory_QS")
        status = json_data.get('Status')
        JobDataStr = json.dumps(json_data)

        conn = connect_to_db()
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        cur.execute("SELECT id FROM JDPresets WHERE uuid = ?", (JD_Uuid,))
        row = cur.fetchone()

        if(row is not None):
            cur.execute("UPDATE JDPresets SET data = ?, date = ?, job = ?, desc = ?, creator = ?, clientName = ?, clientManager = ?, mandQS = ? , status = ? WHERE uuid = ? ", (JobDataStr, Date, JDName, JDDesc, creator, clientName, clientManager, mandQS, status, JD_Uuid))
            print("Updated Data")
        else:
            cur.execute("INSERT INTO JDPresets (uuid, date, job, desc, creator, clientName, clientManager, mandQS , status, data) VALUES (?,  ?, ?, ?, ?, ?, ?, ?, ?, ?)", (JD_Uuid, Date, JDName, JDDesc, creator, clientName, clientManager, mandQS , status, JobDataStr))
            print('Inserting New Row')
        conn.commit()
    except:
        conn().rollback()
        print('Rolled Back')
    finally:
        conn.close()

    return jsonify({"message": "Successfully Saved To DataBase"}), 200

# Route to delete a preset from database
@app.route('/deleteJD', methods=['POST'])
def delete_preset():
    print("HIT")
    message = {}
    try:
        json_data = request.get_json()
        uuid = json_data.get('uuid')
        conn = connect_to_db()
        conn.execute("DELETE from JDPresets WHERE uuid = ?",     
                      (uuid,))
        conn.commit()
        message["status"] = "Preset deleted successfully"
    except:
        conn.rollback()
        message["status"] = "Cannot delete Preset"
    finally:
        conn.close()

    return jsonify(message), 200

if __name__ == "__main__":
    create_db_table()
    app.run(host='0.0.0.0', port=8004, debug=False)
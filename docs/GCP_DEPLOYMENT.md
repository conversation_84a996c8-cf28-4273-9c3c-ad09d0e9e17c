# GCP Deployment Guide for InterviewBot-V2

This guide provides multiple deployment options for deploying InterviewBot-V2 to Google Cloud Platform without requiring a service account JSON key.

## Prerequisites

1. **GCP Project**: You have a GCP project with billing enabled
2. **gcloud CLI**: Installed and authenticated (`gcloud auth login`)
3. **Docker**: Installed locally
4. **Permissions**: Your user account has the following IAM roles:
   - Cloud Run Admin
   - Storage Admin
   - Service Account User
   - Cloud Build Editor (if using Cloud Build)

## Deployment Options

### Option 1: Cloud Run (Recommended for Quick Deployment)

Cloud Run is serverless, scales to zero, and is perfect for getting your app running quickly.

#### Step 1: Deploy using the automated script

```bash
# Make the script executable (already done)
chmod +x scripts/deploy-gcp.sh

# Run the deployment script
./scripts/deploy-gcp.sh YOUR_PROJECT_ID us-central1
```

#### Step 2: Update frontend configuration

After the backend is deployed, you'll get a backend URL. Update your frontend:

1. Edit `app/frontend/.env`:
   ```env
   REACT_APP_API_BASE_URL=https://interviewbot-backend-XXXXXXXXXX-uc.a.run.app
   ```

2. Rebuild and redeploy the frontend:
   ```bash
   docker build -t gcr.io/YOUR_PROJECT_ID/interviewbot-frontend ./app/frontend
   docker push gcr.io/YOUR_PROJECT_ID/interviewbot-frontend
   
   gcloud run deploy interviewbot-frontend \
     --image gcr.io/YOUR_PROJECT_ID/interviewbot-frontend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --port 3000
   ```

### Option 2: Cloud Build (Automated CI/CD)

Use Cloud Build for automated deployments from your repository.

#### Step 1: Enable Cloud Build API

```bash
gcloud services enable cloudbuild.googleapis.com
```

#### Step 2: Deploy using Cloud Build

```bash
gcloud builds submit --config cloudbuild.yaml .
```

### Option 3: Google Kubernetes Engine (GKE)

For more control and container orchestration.

#### Step 1: Create GKE cluster

```bash
gcloud container clusters create interviewbot-cluster \
  --zone us-central1-a \
  --num-nodes 3 \
  --machine-type e2-medium
```

#### Step 2: Get cluster credentials

```bash
gcloud container clusters get-credentials interviewbot-cluster --zone us-central1-a
```

#### Step 3: Update Kubernetes manifests

Replace `PROJECT_ID` in all k8s/*.yaml files with your actual project ID:

```bash
sed -i 's/PROJECT_ID/YOUR_ACTUAL_PROJECT_ID/g' k8s/*.yaml
```

#### Step 4: Deploy to Kubernetes

```bash
# Create namespace
kubectl apply -f k8s/namespace.yaml

# Deploy database
kubectl apply -f k8s/database-deployment.yaml

# Deploy backend
kubectl apply -f k8s/backend-deployment.yaml

# Deploy frontend
kubectl apply -f k8s/frontend-deployment.yaml
```

## Database Strategy

### Current Setup (Quick Deployment)
- Using containerized databases with persistent volumes
- Data persists across container restarts
- Suitable for development and testing

### Recommended Migration (Production)
For production, migrate to managed database services:

1. **Cloud SQL** (PostgreSQL/MySQL)
2. **Firestore** (NoSQL)
3. **Cloud Spanner** (Global scale)

## Environment Configuration

### Frontend Environment Variables

Update `app/frontend/.env` for production:

```env
# Backend API Base URL (update with your actual Cloud Run URL)
REACT_APP_API_BASE_URL=https://interviewbot-backend-XXXXXXXXXX-uc.a.run.app

# Application Environment
REACT_APP_ENV=production

# Production URLs
REACT_APP_REDIRECT_URI=https://your-domain.com
REACT_APP_POST_LOGOUT_REDIRECT_URI=https://your-domain.com
```

## Custom Domain Setup

### Option 1: Cloud Run Custom Domain

```bash
# Map custom domain to Cloud Run service
gcloud run domain-mappings create \
  --service interviewbot-frontend \
  --domain your-domain.com \
  --region us-central1
```

### Option 2: Load Balancer + SSL

For more advanced routing and SSL management, use Google Cloud Load Balancer.

## Monitoring and Logging

### Enable monitoring

```bash
# Cloud Run automatically provides monitoring
# View logs in Cloud Console or via CLI:
gcloud logs read "resource.type=cloud_run_revision"
```

## Security Considerations

1. **IAM**: Use least privilege principle
2. **VPC**: Consider using VPC for internal communication
3. **Secrets**: Use Secret Manager for sensitive data
4. **SSL**: Always use HTTPS in production

## Cost Optimization

1. **Cloud Run**: Scales to zero, pay per request
2. **Resource Limits**: Set appropriate CPU/memory limits
3. **Regional Deployment**: Choose regions close to users
4. **Monitoring**: Set up billing alerts

## Troubleshooting

### Common Issues

1. **Build Failures**: Check Docker build context and dependencies
2. **Service Communication**: Ensure services can reach each other
3. **Environment Variables**: Verify all required env vars are set
4. **Permissions**: Check IAM roles and service account permissions

### Useful Commands

```bash
# View Cloud Run services
gcloud run services list

# View service logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=interviewbot-backend"

# Update service
gcloud run services update interviewbot-backend --region us-central1

# Delete service
gcloud run services delete interviewbot-backend --region us-central1
```

## Next Steps

1. Deploy using Option 1 (Cloud Run) for quick setup
2. Set up custom domain and SSL
3. Configure monitoring and alerting
4. Plan database migration to Cloud SQL
5. Set up CI/CD pipeline with Cloud Build
6. Implement backup and disaster recovery

# Architecture Fix for Cloud Run Deployment

## Problem
The error `Container manifest type 'application/vnd.oci.image.index.v1+json' must support amd64/linux` occurs when Docker images are built on Apple Silicon (ARM64) Macs but Cloud Run requires AMD64/Linux architecture.

## Quick Fix

### Option 1: Use the automated fix script
```bash
./scripts/fix-architecture-deploy.sh chet-crewai us-central1
```

### Option 2: Manual fix commands

1. **Clean up existing images (optional):**
```bash
docker rmi gcr.io/chet-crewai/interviewbot-database:latest 2>/dev/null || true
docker rmi gcr.io/chet-crewai/interviewbot-backend:latest 2>/dev/null || true
docker rmi gcr.io/chet-crewai/interviewbot-frontend:latest 2>/dev/null || true
```

2. **Rebuild with correct architecture:**
```bash
# Database
docker build --platform linux/amd64 -t gcr.io/chet-crewai/interviewbot-database ./db/server
docker push gcr.io/chet-crewai/interviewbot-database

# Backend
docker build --platform linux/amd64 -t gcr.io/chet-crewai/interviewbot-backend ./app/backend
docker push gcr.io/chet-crewai/interviewbot-backend

# Frontend
docker build --platform linux/amd64 -t gcr.io/chet-crewai/interviewbot-frontend ./app/frontend
docker push gcr.io/chet-crewai/interviewbot-frontend
```

3. **Delete existing Cloud Run services:**
```bash
gcloud run services delete interviewbot-database --region=us-central1 --quiet
gcloud run services delete interviewbot-backend --region=us-central1 --quiet
gcloud run services delete interviewbot-frontend --region=us-central1 --quiet
```

4. **Redeploy services:**
```bash
# Database
gcloud run deploy interviewbot-database \
  --image gcr.io/chet-crewai/interviewbot-database \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8003 \
  --memory 1Gi \
  --cpu 1

# Backend
gcloud run deploy interviewbot-backend \
  --image gcr.io/chet-crewai/interviewbot-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8002 \
  --memory 2Gi \
  --cpu 2

# Frontend
gcloud run deploy interviewbot-frontend \
  --image gcr.io/chet-crewai/interviewbot-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 3000 \
  --memory 512Mi \
  --cpu 1
```

## Prevention

Always use `--platform linux/amd64` when building images for Cloud Run:
```bash
docker build --platform linux/amd64 -t your-image-name .
```

## Alternative: Use Cloud Build

Cloud Build automatically uses the correct architecture:
```bash
gcloud builds submit --config cloudbuild.yaml .
```

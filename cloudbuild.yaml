steps:
  # Build backend image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/interviewbot-backend', './app/backend']
    id: 'build-backend'

  # Build frontend image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/interviewbot-frontend', './app/frontend']
    id: 'build-frontend'

  # Build database image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/interviewbot-database', './db/server']
    id: 'build-database'

  # Push backend image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/interviewbot-backend']
    id: 'push-backend'
    waitFor: ['build-backend']

  # Push frontend image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/interviewbot-frontend']
    id: 'push-frontend'
    waitFor: ['build-frontend']

  # Push database image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/interviewbot-database']
    id: 'push-database'
    waitFor: ['build-database']

  # Deploy database service
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'interviewbot-database'
      - '--image=gcr.io/$PROJECT_ID/interviewbot-database'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--port=8003'
      - '--memory=1Gi'
      - '--cpu=1'
    id: 'deploy-database'
    waitFor: ['push-database']

  # Deploy backend service
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'interviewbot-backend'
      - '--image=gcr.io/$PROJECT_ID/interviewbot-backend'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--port=8002'
      - '--memory=2Gi'
      - '--cpu=2'
    id: 'deploy-backend'
    waitFor: ['push-backend', 'deploy-database']

  # Deploy frontend service
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'interviewbot-frontend'
      - '--image=gcr.io/$PROJECT_ID/interviewbot-frontend'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--port=3000'
      - '--memory=512Mi'
      - '--cpu=1'
    id: 'deploy-frontend'
    waitFor: ['push-frontend', 'deploy-backend']

images:
  - 'gcr.io/$PROJECT_ID/interviewbot-backend'
  - 'gcr.io/$PROJECT_ID/interviewbot-frontend'
  - 'gcr.io/$PROJECT_ID/interviewbot-database'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

#!/bin/bash

# Quick fix for the database port issue
# This script only fixes and redeploys the main database service

set -e

PROJECT_ID=${1:-"chet-crewai"}
REGION=${2:-"us-central1"}

echo "🔧 Quick fix for database port issue..."
echo "📍 Project: $PROJECT_ID"
echo "📍 Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Configure Docker for GCP
echo "🔧 Configuring Docker for GCP..."
gcloud auth configure-docker

# Clean up existing database image
echo "🧹 Cleaning up existing database image..."
docker rmi gcr.io/$PROJECT_ID/interviewbot-database:latest 2>/dev/null || true

# Build and push database image with correct architecture and port fix
echo "📦 Building database image with port fix..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-database ./db/server
docker push gcr.io/$PROJECT_ID/interviewbot-database

# Delete existing database service
echo "🗑️  Deleting existing database service..."
gcloud run services delete interviewbot-database --region=$REGION --quiet 2>/dev/null || true

echo "⏳ Waiting for service to be deleted..."
sleep 10

# Deploy database service
echo "🗄️  Deploying database service with correct port..."
gcloud run deploy interviewbot-database \
  --image gcr.io/$PROJECT_ID/interviewbot-database \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8003 \
  --memory 1Gi \
  --cpu 1 \
  --max-instances 10 \
  --timeout 300

# Get database URL
DATABASE_URL=$(gcloud run services describe interviewbot-database --region=$REGION --format="value(status.url)")

echo ""
echo "✅ Database service deployed successfully!"
echo "📋 Database URL: $DATABASE_URL"
echo ""
echo "🔧 Next step: Now run the full deployment script or deploy backend/frontend separately"

#!/bin/bash

# GCP Deployment Script for InterviewBot-V2
# This script deploys the application to Google Cloud Run

set -e

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
REGION=${2:-"us-central1"}

if [ "$PROJECT_ID" = "your-gcp-project-id" ]; then
    echo "Usage: $0 <PROJECT_ID> [REGION]"
    echo "Example: $0 my-project-123 us-central1"
    exit 1
fi

echo "🚀 Deploying InterviewBot-V2 to GCP Project: $PROJECT_ID"
echo "📍 Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Configure Docker for GCP
echo "🔧 Configuring Docker for GCP..."
gcloud auth configure-docker

# Build and push images
echo "🏗️  Building and pushing Docker images..."

# Backend
echo "📦 Building backend image for AMD64 architecture..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-backend ./app/backend
docker push gcr.io/$PROJECT_ID/interviewbot-backend

# Frontend
echo "📦 Building frontend image for AMD64 architecture..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-frontend ./app/frontend
docker push gcr.io/$PROJECT_ID/interviewbot-frontend

# Database
echo "📦 Building database image for AMD64 architecture..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-database ./db/server
docker push gcr.io/$PROJECT_ID/interviewbot-database

# Deploy to Cloud Run
echo "☁️  Deploying to Cloud Run..."

# Deploy database first
echo "🗄️  Deploying database service..."
gcloud run deploy interviewbot-database \
  --image gcr.io/$PROJECT_ID/interviewbot-database \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8003 \
  --memory 1Gi \
  --cpu 1 \
  --max-instances 10

# Get database URL
DATABASE_URL=$(gcloud run services describe interviewbot-database --region=$REGION --format="value(status.url)")

# Deploy backend
echo "🔧 Deploying backend service..."
gcloud run deploy interviewbot-backend \
  --image gcr.io/$PROJECT_ID/interviewbot-backend \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8002 \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10 \
  --set-env-vars DATABASE_URL=$DATABASE_URL

# Get backend URL
BACKEND_URL=$(gcloud run services describe interviewbot-backend --region=$REGION --format="value(status.url)")

# Deploy frontend
echo "🎨 Deploying frontend service..."
gcloud run deploy interviewbot-frontend \
  --image gcr.io/$PROJECT_ID/interviewbot-frontend \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 3000 \
  --memory 512Mi \
  --cpu 1 \
  --max-instances 10

# Get frontend URL
FRONTEND_URL=$(gcloud run services describe interviewbot-frontend --region=$REGION --format="value(status.url)")

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Service URLs:"
echo "   Frontend:  $FRONTEND_URL"
echo "   Backend:   $BACKEND_URL"
echo "   Database:  $DATABASE_URL"
echo ""
echo "🔧 Next steps:"
echo "1. Update your frontend .env file with the backend URL: $BACKEND_URL"
echo "2. Rebuild and redeploy the frontend with the updated environment"
echo "3. Configure your domain DNS to point to: $FRONTEND_URL"
echo "4. Set up SSL certificate if using custom domain"

#!/bin/bash

# Fix Architecture and Redeploy Script
# This script fixes the architecture issue and redeploys with AMD64 images

set -e

# Configuration
PROJECT_ID=${1:-"your-gcp-project-id"}
REGION=${2:-"us-central1"}

if [ "$PROJECT_ID" = "your-gcp-project-id" ]; then
    echo "Usage: $0 <PROJECT_ID> [REGION]"
    echo "Example: $0 chet-crewai us-central1"
    exit 1
fi

echo "🔧 Fixing architecture issue and redeploying..."
echo "📍 Project: $PROJECT_ID"
echo "📍 Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Configure Docker for GCP
echo "🔧 Configuring Docker for GCP..."
gcloud auth configure-docker

# Clean up existing images (optional - comment out if you want to keep them)
echo "🧹 Cleaning up existing images..."
docker rmi gcr.io/$PROJECT_ID/interviewbot-backend:latest 2>/dev/null || true
docker rmi gcr.io/$PROJECT_ID/interviewbot-frontend:latest 2>/dev/null || true
docker rmi gcr.io/$PROJECT_ID/interviewbot-database:latest 2>/dev/null || true

# Build and push images with correct architecture
echo "🏗️  Building and pushing Docker images with AMD64 architecture..."

# Database first (since backend depends on it)
echo "📦 Building database image..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-database ./db/server
docker push gcr.io/$PROJECT_ID/interviewbot-database

# Backend
echo "📦 Building backend image..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-backend ./app/backend
docker push gcr.io/$PROJECT_ID/interviewbot-backend

# Frontend
echo "📦 Building frontend image..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-frontend ./app/frontend
docker push gcr.io/$PROJECT_ID/interviewbot-frontend

# Delete existing Cloud Run services to ensure clean deployment
echo "🗑️  Cleaning up existing Cloud Run services..."
gcloud run services delete interviewbot-database --region=$REGION --quiet 2>/dev/null || true
gcloud run services delete interviewbot-backend --region=$REGION --quiet 2>/dev/null || true
gcloud run services delete interviewbot-frontend --region=$REGION --quiet 2>/dev/null || true

echo "⏳ Waiting for services to be deleted..."
sleep 10

# Deploy to Cloud Run with fresh services
echo "☁️  Deploying to Cloud Run..."

# Deploy database first
echo "🗄️  Deploying database service..."
gcloud run deploy interviewbot-database \
  --image gcr.io/$PROJECT_ID/interviewbot-database \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8003 \
  --memory 1Gi \
  --cpu 1 \
  --max-instances 10 \
  --timeout 300

# Get database URL
DATABASE_URL=$(gcloud run services describe interviewbot-database --region=$REGION --format="value(status.url)")
echo "✅ Database deployed at: $DATABASE_URL"

# Deploy backend
echo "🔧 Deploying backend service..."
gcloud run deploy interviewbot-backend \
  --image gcr.io/$PROJECT_ID/interviewbot-backend \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8002 \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10 \
  --timeout 300 \
  --set-env-vars DATABASE_URL=$DATABASE_URL

# Get backend URL
BACKEND_URL=$(gcloud run services describe interviewbot-backend --region=$REGION --format="value(status.url)")
echo "✅ Backend deployed at: $BACKEND_URL"

# Deploy frontend
echo "🎨 Deploying frontend service..."
gcloud run deploy interviewbot-frontend \
  --image gcr.io/$PROJECT_ID/interviewbot-frontend \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 3000 \
  --memory 512Mi \
  --cpu 1 \
  --max-instances 10 \
  --timeout 300

# Get frontend URL
FRONTEND_URL=$(gcloud run services describe interviewbot-frontend --region=$REGION --format="value(status.url)")

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Service URLs:"
echo "   Frontend:  $FRONTEND_URL"
echo "   Backend:   $BACKEND_URL"
echo "   Database:  $DATABASE_URL"
echo ""
echo "🔧 Next steps:"
echo "1. Update your frontend .env file with the backend URL:"
echo "   REACT_APP_API_BASE_URL=$BACKEND_URL"
echo "2. Test the services by visiting: $FRONTEND_URL"
echo "3. If frontend needs backend URL update, rebuild and redeploy frontend"

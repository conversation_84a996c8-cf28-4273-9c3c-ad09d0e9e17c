#!/bin/bash

# Quick fix for the frontend deployment
# This script builds and deploys the frontend with the updated backend URL

set -e

PROJECT_ID=${1:-"chet-crewai"}
REGION=${2:-"us-central1"}

echo "🎨 Deploying frontend with updated backend URL..."
echo "📍 Project: $PROJECT_ID"
echo "📍 Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Configure Docker for GCP
echo "🔧 Configuring Docker for GCP..."
gcloud auth configure-docker

# Clean up existing frontend image
echo "🧹 Cleaning up existing frontend image..."
docker rmi gcr.io/$PROJECT_ID/interviewbot-frontend:latest 2>/dev/null || true

# Build and push frontend image with correct architecture and updated env
echo "📦 Building frontend image with updated backend URL..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-frontend ./app/frontend
docker push gcr.io/$PROJECT_ID/interviewbot-frontend

# Delete existing frontend service
echo "🗑️  Deleting existing frontend service..."
gcloud run services delete interviewbot-frontend --region=$REGION --quiet 2>/dev/null || true

echo "⏳ Waiting for service to be deleted..."
sleep 10

# Deploy frontend service
echo "🎨 Deploying frontend service..."
gcloud run deploy interviewbot-frontend \
  --image gcr.io/$PROJECT_ID/interviewbot-frontend \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 3000 \
  --memory 512Mi \
  --cpu 1 \
  --max-instances 10 \
  --timeout 300

# Get frontend URL
FRONTEND_URL=$(gcloud run services describe interviewbot-frontend --region=$REGION --format="value(status.url)")

echo ""
echo "✅ Frontend service deployed successfully!"
echo "📋 Frontend URL: $FRONTEND_URL"
echo ""
echo "🎉 Your InterviewBot-V2 application is now live!"
echo "🌐 Visit: $FRONTEND_URL"

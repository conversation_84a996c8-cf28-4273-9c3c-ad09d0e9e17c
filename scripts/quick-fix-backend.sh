#!/bin/bash

# Quick fix for the backend port issue
# This script only fixes and redeploys the backend service

set -e

PROJECT_ID=${1:-"chet-crewai"}
REGION=${2:-"us-central1"}

echo "🔧 Quick fix for backend port issue..."
echo "📍 Project: $PROJECT_ID"
echo "📍 Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Configure Docker for GCP
echo "🔧 Configuring Docker for GCP..."
gcloud auth configure-docker

# Clean up existing backend image
echo "🧹 Cleaning up existing backend image..."
docker rmi gcr.io/$PROJECT_ID/interviewbot-backend:latest 2>/dev/null || true

# Build and push backend image with correct architecture and port fix
echo "📦 Building backend image with port fix..."
docker build --platform linux/amd64 -t gcr.io/$PROJECT_ID/interviewbot-backend ./app/backend
docker push gcr.io/$PROJECT_ID/interviewbot-backend

# Delete existing backend service
echo "🗑️  Deleting existing backend service..."
gcloud run services delete interviewbot-backend --region=$REGION --quiet 2>/dev/null || true

echo "⏳ Waiting for service to be deleted..."
sleep 10

# Get database URL for backend environment
DATABASE_URL=$(gcloud run services describe interviewbot-database --region=$REGION --format="value(status.url)" 2>/dev/null || echo "")

# Deploy backend service
echo "🔧 Deploying backend service with correct port..."
if [ -n "$DATABASE_URL" ]; then
    echo "🔗 Using database URL: $DATABASE_URL"
    gcloud run deploy interviewbot-backend \
      --image gcr.io/$PROJECT_ID/interviewbot-backend \
      --platform managed \
      --region $REGION \
      --allow-unauthenticated \
      --port 8002 \
      --memory 2Gi \
      --cpu 2 \
      --max-instances 10 \
      --timeout 300 \
      --set-env-vars DATABASE_URL=$DATABASE_URL
else
    echo "⚠️  No database URL found, deploying without DATABASE_URL env var"
    gcloud run deploy interviewbot-backend \
      --image gcr.io/$PROJECT_ID/interviewbot-backend \
      --platform managed \
      --region $REGION \
      --allow-unauthenticated \
      --port 8002 \
      --memory 2Gi \
      --cpu 2 \
      --max-instances 10 \
      --timeout 300
fi

# Get backend URL
BACKEND_URL=$(gcloud run services describe interviewbot-backend --region=$REGION --format="value(status.url)")

echo ""
echo "✅ Backend service deployed successfully!"
echo "📋 Backend URL: $BACKEND_URL"
echo ""
echo "🔧 Next step: Update frontend .env file with this backend URL and redeploy frontend"
echo "   REACT_APP_API_BASE_URL=$BACKEND_URL"

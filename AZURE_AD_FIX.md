# 🔧 Fix Azure AD Redirect URI Issue

## Problem
The frontend is redirecting to `http://localhost:3000` because Azure AD app registration still has localhost configured as the redirect URI instead of your production Cloud Run URL.

## Solution

### Step 1: Update Azure AD App Registration

1. **Go to Azure Portal**: https://portal.azure.com

2. **Navigate to Azure Active Directory**:
   - Click "Azure Active Directory" in the left sidebar
   - Click "App registrations"

3. **Find Your App**:
   - Search for your InterviewBot app
   - Client ID: `04efb143-adb9-43eb-ab40-e8bfca657adb`
   - Or search by name if you know it

4. **Update Authentication Settings**:
   - Click on your app to open it
   - Click "Authentication" in the left sidebar

5. **Add Production Redirect URI**:
   - Under "Platform configurations", find "Single-page application"
   - Click "Add URI" or edit existing URIs
   - Add: `https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app`
   - **Important**: Keep the localhost URI for development if needed

6. **Update Logout URL**:
   - Scroll down to "Front-channel logout URL"
   - Set to: `https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app`

7. **Save Changes**:
   - Click "Save" at the top of the page

### Step 2: Verify Configuration

Your Azure AD app should now have these URIs configured:

**Redirect URIs (Single-page application)**:
- `http://localhost:3000` (for development)
- `https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app` (for production)

**Front-channel logout URL**:
- `https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app`

### Step 3: Test the Application

1. **Clear browser cache** and cookies for the domain
2. **Visit your production URL**: https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app
3. **Try to login** - it should now redirect properly to your production URL after authentication

## Current Configuration

Your `.env` file has been updated with:

```env
# Production URLs (using actual Cloud Run URL)
REACT_APP_REDIRECT_URI=https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app
REACT_APP_POST_LOGOUT_REDIRECT_URI=https://interviewbot-frontend-x4wlfpo63q-uc.a.run.app
```

## Troubleshooting

### If you still get localhost redirects:

1. **Check browser cache**: Clear all cookies and cache
2. **Verify Azure AD config**: Double-check the redirect URIs are saved
3. **Wait for propagation**: Azure AD changes can take a few minutes
4. **Check browser developer tools**: Look for any authentication errors in console

### If you get "redirect_uri_mismatch" error:

1. **Copy the exact URL** from the error message
2. **Add that exact URL** to Azure AD redirect URIs
3. **Make sure there are no trailing slashes** or extra characters

## Alternative: Use Custom Domain

If you want to use `https://interviews.creospan.com`:

1. **Set up custom domain** in Cloud Run
2. **Update DNS** to point to Cloud Run
3. **Update Azure AD** with the custom domain
4. **Update .env file** with the custom domain

## Security Notes

- ✅ Always use HTTPS for production redirect URIs
- ✅ Never use localhost in production Azure AD config
- ✅ Keep development and production URIs separate
- ✅ Regularly review and clean up unused redirect URIs
